import { ReactNode } from "react";
import { AppSidebar } from "@/components/AppSidebar";
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/lib/auth";

export default function AppLayout({ children }: { children: ReactNode }) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, signOut } = useAuth();

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <SidebarInset>
          <header className="sticky top-0 z-10 bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
            <div className="h-14 flex items-center gap-3 px-4">
              <SidebarTrigger />
              <div className="flex items-center gap-2">
                <div className="h-6 w-6 rounded-md bg-gradient-to-br from-brand to-brand-soft" aria-hidden />
                <span className="font-semibold">Daily Angel Admin</span>
              </div>
              <div className="ml-auto flex items-center gap-2">
                {location.pathname !== "/ngos/new" && (
                  <Button variant="hero" size="sm" onClick={() => navigate("/ngos/new")}>+ New NGO</Button>
                )}
                {location.pathname !== "/categories" && (
                  <Button variant="secondary" size="sm" onClick={() => navigate("/categories")}>Manage Categories</Button>
                )}
                {user && (
                  <Button variant="outline" size="sm" onClick={() => signOut().then(() => navigate('/login'))}>Sign out</Button>
                )}
              </div>
            </div>
          </header>
          <main className="p-4">
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
