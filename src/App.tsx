import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import NGOsList from "./pages/NGOsList";
import NGOEdit from "./pages/NGOEdit";
import ActivitiesList from "./pages/ActivitiesList";
import Categories from "./pages/Categories";
import AppLayout from "./layouts/AppLayout";
import Login from "./pages/Login";
import Unauthorized from "./pages/Unauthorized";
import { AuthProvider, ProtectedRoute } from "./lib/auth";
import Admins from "./pages/Admins";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/unauthorized" element={<Unauthorized />} />

            <Route
              path="/"
              element={<ProtectedRoute><AppLayout><Index /></AppLayout></ProtectedRoute>}
            />
            <Route
              path="/ngos"
              element={<ProtectedRoute><AppLayout><NGOsList /></AppLayout></ProtectedRoute>}
            />
            <Route
              path="/ngos/new"
              element={<ProtectedRoute><AppLayout><NGOEdit /></AppLayout></ProtectedRoute>}
            />
            <Route
              path="/ngos/:id"
              element={<ProtectedRoute><AppLayout><NGOEdit /></AppLayout></ProtectedRoute>}
            />
            <Route
              path="/activities"
              element={<ProtectedRoute><AppLayout><ActivitiesList /></AppLayout></ProtectedRoute>}
            />
            <Route
              path="/categories"
              element={<ProtectedRoute><AppLayout><Categories /></AppLayout></ProtectedRoute>}
            />
            <Route
              path="/admins"
              element={<ProtectedRoute><AppLayout><Admins /></AppLayout></ProtectedRoute>}
            />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
