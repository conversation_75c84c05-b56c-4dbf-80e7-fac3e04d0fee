import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import NGOsList from "./pages/NGOsList";
import NGOEdit from "./pages/NGOEdit";
import ActivitiesList from "./pages/ActivitiesList";
import Categories from "./pages/Categories";
import AppLayout from "./layouts/AppLayout";
import Login from "./pages/Login";
import Unauthorized from "./pages/Unauthorized";
import { AuthProvider, ProtectedRoute } from "./lib/auth";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <AppLayout>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/unauthorized" element={<Unauthorized />} />

              <Route path="/" element={<ProtectedRoute><Index /></ProtectedRoute>} />
              <Route path="/ngos" element={<ProtectedRoute><NGOsList /></ProtectedRoute>} />
              <Route path="/ngos/new" element={<ProtectedRoute><NGOEdit /></ProtectedRoute>} />
              <Route path="/ngos/:id" element={<ProtectedRoute><NGOEdit /></ProtectedRoute>} />
              <Route path="/activities" element={<ProtectedRoute><ActivitiesList /></ProtectedRoute>} />
              <Route path="/categories" element={<ProtectedRoute><Categories /></ProtectedRoute>} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </AppLayout>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
