import { ReactNode, createContext, useContext, useEffect, useMemo, useState } from 'react';
import { User, onAuthStateChanged, signInWithPopup, signOut, signInWithEmailAndPassword, createUserWithEmailAndPassword, sendPasswordResetEmail } from 'firebase/auth';
import { auth, googleProvider } from './firebase';
import { isEmailAdmin } from '@/store/store';

type AuthContextValue = {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAdmin: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  registerWithEmail: (email: string, password: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
};


const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const unsub = onAuthStateChanged(auth, (u) => {
      setUser(u);
      setLoading(false);
    }, (e) => {
      setError(e?.message || String(e));
      setLoading(false);
    });
    return () => unsub();
  }, []);

  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [adminChecking, setAdminChecking] = useState<boolean>(true);

  useEffect(() => {
    let alive = true;
    (async () => {
      if (!user?.email) { setIsAdmin(false); setAdminChecking(false); return; }
      setAdminChecking(true);
      try {
        const ok = await isEmailAdmin(user.email);
        if (alive) setIsAdmin(ok);
      } catch (e) {
        if (alive) setIsAdmin(false);
      }
      if (alive) setAdminChecking(false);
    })();
    return () => { alive = false; };
  }, [user?.email]);

  const signInWithGoogleHandler = async (): Promise<void> => {
    setError(null);
    try {
      await signInWithPopup(auth, googleProvider);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      throw e;
    }
  };

  const signOutHandler = async (): Promise<void> => {
    setError(null);
    try {
      await signOut(auth);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      throw e;
    }
  };

  const signInWithEmail = async (email: string, password: string): Promise<void> => {
    setError(null);
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      throw e;
    }
  };

  const registerWithEmail = async (email: string, password: string): Promise<void> => {
    setError(null);
    try {
      await createUserWithEmailAndPassword(auth, email, password);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      throw e;
    }
  };

  const resetPassword = async (email: string): Promise<void> => {
    setError(null);
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      throw e;
    }
  };

  const value = useMemo<AuthContextValue>(() => ({
    user,
    loading: loading || adminChecking,
    error,
    isAdmin,
    signInWithGoogle: signInWithGoogleHandler,
    signOut: signOutHandler,
    signInWithEmail,
    registerWithEmail,
    resetPassword,
  }), [user, loading, adminChecking, error, isAdmin]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within <AuthProvider>');
  return ctx;
}

export function ProtectedRoute({ children }: { children: ReactNode }) {
  const { user, loading, isAdmin } = useAuth();
  if (loading) return null; // or a spinner component
  if (!user) {
    window.location.href = '/login';
    return null;
  }
  if (!isAdmin) {
    window.location.href = '/unauthorized';
    return null;
  }
  return <>{children}</>;
}


