import { ReactNode, createContext, useContext, useEffect, useMemo, useState } from 'react';
import { User, onAuthStateChanged, signInWithPopup, signOut } from 'firebase/auth';
import { auth, googleProvider } from './firebase';

type AuthContextValue = {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAdmin: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
};

const ADMIN_EMAIL = '<EMAIL>';

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const unsub = onAuthStateChanged(auth, (u) => {
      setUser(u);
      setLoading(false);
    }, (e) => {
      setError(e?.message || String(e));
      setLoading(false);
    });
    return () => unsub();
  }, []);

  const isAdmin = useMemo(() => !!user && user.email === ADMIN_EMAIL, [user]);

  const signInWithGoogleHandler = async (): Promise<void> => {
    setError(null);
    try {
      await signInWithPopup(auth, googleProvider);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      throw e;
    }
  };

  const signOutHandler = async (): Promise<void> => {
    setError(null);
    try {
      await signOut(auth);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      throw e;
    }
  };

  const value = useMemo<AuthContextValue>(() => ({
    user,
    loading,
    error,
    isAdmin,
    signInWithGoogle: signInWithGoogleHandler,
    signOut: signOutHandler,
  }), [user, loading, error, isAdmin]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within <AuthProvider>');
  return ctx;
}

export function ProtectedRoute({ children }: { children: ReactNode }) {
  const { user, loading, isAdmin } = useAuth();
  if (loading) return null; // or a spinner component
  if (!user) {
    window.location.href = '/login';
    return null;
  }
  if (!isAdmin) {
    window.location.href = '/unauthorized';
    return null;
  }
  return <>{children}</>;
}


