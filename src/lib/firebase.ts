import { initializeApp, getApps, getApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';

// NOTE: Replace these with your own project if needed. Currently using project "daily-angel".
const firebaseConfig = {
    apiKey: "AIzaSyAuPQt6JYCdKOxir3-vPD7D2NDdeVhsgm4",
    authDomain: "daily-angel.firebaseapp.com",
    projectId: "daily-angel",
    storageBucket: "daily-angel.firebasestorage.app",
    messagingSenderId: "284025320236",
    appId: "1:284025320236:web:75f54856834f4466bf3289",
    measurementId: "G-TS1VQEL0WP"
};

const app = getApps().length ? getApp() : initializeApp(firebaseConfig);

export const db = getFirestore(app);
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();


