import { db } from '@/lib/firebase';
import { addDoc, collection, deleteDoc, doc, getDocs, onSnapshot, orderBy, query, setDoc, updateDoc, where, writeBatch } from 'firebase/firestore';
export type NGOStatus = 'draft' | 'submitted' | 'approved' | 'rejected' | 'suspended';
export type ActivityStatus = 'draft' | 'submitted' | 'approved' | 'live' | 'completed';

export interface NGO {
  id: string;
  legalName: string;
  displayName: string;
  country: string; // ISO-2
  email: string;
  phone?: string;
  website?: string;
  address?: string;
  status: NGOStatus;
  categories: string[]; // category ids
  createdAt: number;
  updatedAt: number;
  createdBy?: string;
  updatedBy?: string;
}

export interface Activity {
  id: string;
  ngoId: string;
  title: string;
  shortDescription?: string;
  longDescription?: string;
  categoryId?: string;
  status: ActivityStatus;
  startAt?: number | null;
  endAt?: number | null;
  coverImageUrl?: string | null;
  createdAt: number;
  updatedAt: number;
  createdBy?: string;
  updatedBy?: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  order: number;
  isActive: boolean;
  color?: string | null;
  parentCategory?: string | null;
  subCategories?: string[];
  createdAt: number;
  updatedAt: number;
}

export interface Audit {
  id: string;
  entity: 'ngo' | 'activity' | 'category';
  entityId: string;
  action: 'create'|'update'|'approve'|'reject'|'suspend'|'archive';
  actorId?: string;
  before?: unknown | null;
  after?: unknown | null;
  at: number;
}

// ===== Admins: Firestore-backed implementation =====
export interface AdminUser {
  id: string;
  email: string;
  createdAt: number;
}

const ADMINS_COLLECTION = 'admins';
const adminsRef = collection(db, ADMINS_COLLECTION);
// Legacy location: /admin/admin_list/admins/*
const legacyAdminsRef = collection(db, 'admin', 'admin_list', 'admins');

export async function fetchAdmins(): Promise<AdminUser[]> {
  const snap = await getDocs(adminsRef);
  return snap.docs.map(d => {
    const data = d.data() as { email: string; createdAt: number };
    return { id: d.id, email: data.email, createdAt: data.createdAt };
  });
}

export function subscribeAdmins(handler: (admins: AdminUser[]) => void) {
  return onSnapshot(adminsRef, (snap) => {
    const list = snap.docs.map(d => {
      const data = d.data() as { email: string; createdAt: number };
      return { id: d.id, email: data.email, createdAt: data.createdAt };
    });
    handler(list);
  });
}

export async function isEmailAdmin(email: string | null | undefined): Promise<boolean> {
  if (!email) return false;
  const normalized = email.toLowerCase();
  // Check new collection
  const qAdmins = query(adminsRef, where('email', '==', normalized));
  const snap = await getDocs(qAdmins);
  if (!snap.empty) return true;
  // Fallback: legacy path /admin/admin_list/admins
  const qLegacy = query(legacyAdminsRef, where('email', '==', normalized));
  const legacySnap = await getDocs(qLegacy);
  return !legacySnap.empty;
}

export async function addAdminEmail(email: string): Promise<AdminUser> {
  const normalized = email.toLowerCase().trim();
  const now = Date.now();
  // prevent duplicates
  if (await isEmailAdmin(normalized)) {
    const existing = await getDocs(query(adminsRef, where('email', '==', normalized)));
    const d = existing.docs[0];
    const data = d.data() as { email: string; createdAt: number };
    return { id: d.id, email: data.email, createdAt: data.createdAt };
  }
  const ref = await addDoc(adminsRef, { email: normalized, createdAt: now });
  return { id: ref.id, email: normalized, createdAt: now } as AdminUser;
}

export async function deleteAdmin(id: string): Promise<void> {
  await deleteDoc(doc(db, ADMINS_COLLECTION, id));
}

const K = {
  ngos: 'da_ngos',
  acts: 'da_activities',
  cats: 'da_categories',
  audits: 'da_audits',
};

function load<T>(key: string): T[] {
  try { return JSON.parse(localStorage.getItem(key) || '[]') as T[] } catch { return [] as T[] }
}
function save<T>(key: string, value: T[]) {
  localStorage.setItem(key, JSON.stringify(value));
}
function genId(prefix: string) { return `${prefix}_${crypto.randomUUID?.() || Date.now()}`; }

export function listNGOs() { return load<NGO>(K.ngos); }
export function getNGO(id: string) { return listNGOs().find(n => n.id === id); }
export function upsertNGO(input: Partial<NGO> & Pick<NGO, 'legalName'|'displayName'|'country'|'email'> & { id?: string }) {
  const now = Date.now();
  const items = listNGOs();
  let ngo: NGO;
  if (input.id) {
    const idx = items.findIndex(n => n.id === input.id);
    if (idx >= 0) {
      const before = items[idx];
      ngo = { ...before, ...input, updatedAt: now } as NGO;
      items[idx] = ngo;
      addAudit({ entity: 'ngo', entityId: ngo.id, action: 'update', before, after: ngo });
    } else {
      ngo = { ...(input as NGO), id: input.id, status: 'draft', categories: [], createdAt: now, updatedAt: now } as NGO;
      items.push(ngo);
      addAudit({ entity: 'ngo', entityId: ngo.id, action: 'create', after: ngo });
    }
  } else {
    ngo = {
      id: genId('ngo'),
      legalName: input.legalName,
      displayName: input.displayName,
      country: input.country,
      email: input.email,
      phone: input.phone,
      website: input.website,
      address: input.address,
      status: (input.status as NGOStatus) || 'draft',
      categories: input.categories || [],
      createdAt: now,
      updatedAt: now,
    };
    items.push(ngo);
    addAudit({ entity: 'ngo', entityId: ngo.id, action: 'create', after: ngo });
  }
  save(K.ngos, items);
  return ngo;
}

export function setNGOStatus(id: string, status: NGOStatus) {
  const items = listNGOs();
  const idx = items.findIndex(n => n.id === id);
  if (idx < 0) return;
  const before = items[idx];
  const after = { ...before, status, updatedAt: Date.now() } as NGO;
  items[idx] = after;
  save(K.ngos, items);
  addAudit({ entity: 'ngo', entityId: id, action: status === 'approved' ? 'approve' : status === 'rejected' ? 'reject' : status === 'suspended' ? 'suspend' : 'update', before, after });
}

export function listActivities() { return load<Activity>(K.acts); }
export function listActivitiesByNGO(ngoId: string) { return listActivities().filter(a => a.ngoId === ngoId); }
export function upsertActivity(input: Partial<Activity> & Pick<Activity, 'ngoId'|'title'> & { id?: string }) {
  const now = Date.now();
  const items = listActivities();
  let act: Activity;
  if (input.id) {
    const idx = items.findIndex(a => a.id === input.id);
    if (idx >= 0) {
      const before = items[idx];
      act = { ...before, ...input, updatedAt: now } as Activity;
      items[idx] = act;
      addAudit({ entity: 'activity', entityId: act.id, action: 'update', before, after: act });
    } else {
      act = { ...(input as Activity), id: input.id, status: 'draft', createdAt: now, updatedAt: now } as Activity;
      items.push(act);
      addAudit({ entity: 'activity', entityId: act.id, action: 'create', after: act });
    }
  } else {
    act = {
      id: genId('act'),
      ngoId: input.ngoId,
      title: input.title,
      shortDescription: input.shortDescription,
      longDescription: input.longDescription,
      categoryId: input.categoryId,
      status: (input.status as ActivityStatus) || 'draft',
      startAt: input.startAt ?? null,
      endAt: input.endAt ?? null,
      coverImageUrl: input.coverImageUrl ?? null,
      createdAt: now,
      updatedAt: now,
    };
    items.push(act);
    addAudit({ entity: 'activity', entityId: act.id, action: 'create', after: act });
  }
  save(K.acts, items);
  return act;
}

export function setActivityStatus(id: string, status: ActivityStatus) {
  const items = listActivities();
  const idx = items.findIndex(a => a.id === id);
  if (idx < 0) return;
  const before = items[idx];
  const after = { ...before, status, updatedAt: Date.now() } as Activity;
  items[idx] = after;
  save(K.acts, items);
  addAudit({ entity: 'activity', entityId: id, action: status === 'approved' ? 'approve' : status === 'completed' ? 'archive' : 'update', before, after });
}

// ===== Categories: Firestore-backed implementation =====
const CATEGORIES_COLLECTION = 'categories';
const categoriesRef = collection(db, CATEGORIES_COLLECTION);

let categoriesCache: Category[] = [];
let categoriesSubscribed = false;

type FirestoreCategory = {
  name: string;
  description?: string;
  slug: string;
  order?: number;
  isActive?: boolean;
  color?: string | null;
  parentCategory?: string | null;
  subCategories?: string[];
  createdAt?: number;
  updatedAt?: number;
};

function mapCategoryDoc(d: FirestoreCategory, id: string, indexFallback: number): Category {
  return {
    id,
    name: d.name,
    description: d.description ?? '',
    slug: d.slug,
    order: typeof d.order === 'number' ? d.order : indexFallback,
    isActive: d.isActive ?? true,
    color: d.color ?? null,
    parentCategory: d.parentCategory ?? null,
    subCategories: Array.isArray(d.subCategories) ? d.subCategories : [],
    createdAt: typeof d.createdAt === 'number' ? d.createdAt : Date.now(),
    updatedAt: typeof d.updatedAt === 'number' ? d.updatedAt : Date.now(),
  } as Category;
}

function ensureCategoriesSubscription() {
  if (categoriesSubscribed) return;
  const q = query(categoriesRef, orderBy('order', 'asc'));
  onSnapshot(q, (snap) => {
    const next = snap.docs.map((docSnap, idx) => mapCategoryDoc(docSnap.data() as FirestoreCategory, docSnap.id, idx));
    categoriesCache = next;
  });
  categoriesSubscribed = true;
}

export function subscribeCategories(
  handler: (cats: Category[]) => void,
  onError?: (error: Error) => void
) {
  const qOrdered = query(categoriesRef, orderBy('order', 'asc'));
  const unsub = onSnapshot(
    qOrdered,
    (snap) => {
      const cats = snap.docs.map((d, idx) => mapCategoryDoc(d.data() as FirestoreCategory, d.id, idx));
      categoriesCache = cats;
      handler(cats);
    },
    (err) => {
      // Fallback: no orderBy
      const fbUnsub = onSnapshot(categoriesRef, (snap) => {
        const cats = snap.docs.map((d, idx) => mapCategoryDoc(d.data() as FirestoreCategory, d.id, idx));
        cats.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
        categoriesCache = cats;
        handler(cats);
      });
      onError?.(err as Error);
      unsub();
      return fbUnsub;
    }
  );
  return unsub;
}

export async function fetchCategories(): Promise<Category[]> {
  try {
    const qOrdered = query(categoriesRef, orderBy('order', 'asc'));
    const snap = await getDocs(qOrdered);
    return snap.docs.map((d, idx) => mapCategoryDoc(d.data() as FirestoreCategory, d.id, idx));
  } catch (e) {
    const snap = await getDocs(categoriesRef);
    const cats = snap.docs.map((d, idx) => mapCategoryDoc(d.data() as FirestoreCategory, d.id, idx));
    cats.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
    return cats;
  }
}

export function listCategories() { ensureCategoriesSubscription(); return categoriesCache; }
export async function upsertCategory(input: Partial<Category> & Pick<Category,'name'> & { id?: string }) {
  const now = Date.now();
  if (input.id) {
    const ref = doc(db, CATEGORIES_COLLECTION, input.id);
    const update = {
      name: input.name,
      description: input.description ?? undefined,
      slug: input.slug ?? slugify(input.name!),
      isActive: input.isActive ?? true,
      color: input.color ?? null,
      parentCategory: input.parentCategory ?? null,
      subCategories: input.subCategories ?? [],
      updatedAt: now,
    };
    await setDoc(ref, update, { merge: true });
    return { id: input.id, order: input.order ?? 0, createdAt: input.createdAt ?? now, ...update } as Category;
  }
  const order = (typeof input.order === 'number' ? input.order : categoriesCache.length) as number;
  const docRef = await addDoc(categoriesRef, {
    name: input.name,
    description: input.description ?? '',
    slug: input.slug ?? slugify(input.name!),
    order,
    isActive: input.isActive ?? true,
    color: input.color ?? null,
    parentCategory: input.parentCategory ?? null,
    subCategories: input.subCategories ?? [],
    createdAt: now,
    updatedAt: now,
  });
  return {
    id: docRef.id,
    name: input.name!,
    description: input.description ?? '',
    slug: input.slug ?? slugify(input.name!),
    order,
    isActive: input.isActive ?? true,
    color: input.color ?? null,
    parentCategory: input.parentCategory ?? null,
    subCategories: input.subCategories ?? [],
    createdAt: now,
    updatedAt: now,
  } as Category;
}
export async function reorderCategories(idsInOrder: string[]) {
  const batch = writeBatch(db);
  idsInOrder.forEach((id, idx) => {
    const ref = doc(db, CATEGORIES_COLLECTION, id);
    batch.update(ref, { order: idx, updatedAt: Date.now() });
  });
  await batch.commit();
}
export async function toggleCategoryActive(id: string, isActive: boolean) {
  const ref = doc(db, CATEGORIES_COLLECTION, id);
  await updateDoc(ref, { isActive, updatedAt: Date.now() });
}

export function addAudit(a: Omit<Audit, 'id'|'at'> & { at?: number }) {
  const items = load<Audit>(K.audits);
  const entry: Audit = { id: genId('audit'), at: a.at ?? Date.now(), ...a } as Audit;
  items.unshift(entry);
  save(K.audits, items);
  return entry;
}

export function countPending() {
  const ngos = listNGOs().filter(n => n.status === 'submitted').length;
  const acts = listActivities().filter(a => a.status === 'submitted').length;
  return { ngos, acts };
}

function slugify(s: string) {
  return s.toLowerCase().trim().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
}
