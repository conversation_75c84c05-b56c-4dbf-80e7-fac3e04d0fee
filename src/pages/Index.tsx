import { useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Link } from "react-router-dom";
import { countPending, listActivities, listNGOs } from "@/store/store";

const Index = () => {
  const stats = useMemo(() => countPending(), []);
  const submittedNGOs = useMemo(() => listNGOs().filter(n => n.status === 'submitted').slice(0,5), []);
  const submittedActs = useMemo(() => listActivities().filter(a => a.status === 'submitted').slice(0,5), []);

  return (
    <div className="space-y-6">
      <header>
        <h1 className="text-2xl font-semibold">Dashboard</h1>
        <p className="text-sm text-muted-foreground">Quick overview and review queues.</p>
      </header>

      <section className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>Pending NGOs</CardTitle>
          </CardHeader>
          <CardContent className="flex items-end justify-between">
            <div className="text-4xl font-bold">{stats.ngos}</div>
            <Button asChild variant="link"><Link to="/ngos">View all</Link></Button>
          </CardContent>
        </Card>
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>Pending Activities</CardTitle>
          </CardHeader>
          <CardContent className="flex items-end justify-between">
            <div className="text-4xl font-bold">{stats.acts}</div>
            <Button asChild variant="link"><Link to="/activities">View all</Link></Button>
          </CardContent>
        </Card>
      </section>

      <section className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>NGOs → submitted</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Country</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {submittedNGOs.length === 0 && (
                  <TableRow><TableCell colSpan={3} className="text-muted-foreground">No submitted NGOs</TableCell></TableRow>
                )}
                {submittedNGOs.map(n => (
                  <TableRow key={n.id}>
                    <TableCell className="font-medium">{n.displayName}</TableCell>
                    <TableCell>{n.country}</TableCell>
                    <TableCell>{n.status}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Activities → submitted</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>NGO</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {submittedActs.length === 0 && (
                  <TableRow><TableCell colSpan={3} className="text-muted-foreground">No submitted activities</TableCell></TableRow>
                )}
                {submittedActs.map(a => (
                  <TableRow key={a.id}>
                    <TableCell className="font-medium">{a.title}</TableCell>
                    <TableCell>{a.ngoId}</TableCell>
                    <TableCell>{a.status}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </section>
    </div>
  );
};

export default Index;
