import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

export default function Unauthorized() {
  return (
    <div className="max-w-md mx-auto text-center space-y-4">
      <h1 className="text-2xl font-semibold">Unauthorized</h1>
      <p className="text-muted-foreground">Your account is not authorized to access the admin dashboard.</p>
      <div className="space-x-2">
        <Button asChild variant="secondary"><Link to="/">Home</Link></Button>
        <Button asChild><Link to="/login">Try another account</Link></Button>
      </div>
    </div>
  );
}


