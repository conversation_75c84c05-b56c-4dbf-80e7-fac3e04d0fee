import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getNGO, NGOStatus, setNGOStatus, upsertNGO } from "@/store/store";
import { toast } from "@/hooks/use-toast";

const schema = z.object({
  id: z.string().optional(),
  legalName: z.string().min(2),
  displayName: z.string().min(2),
  country: z.string().length(2, 'ISO-2 country code'),
  email: z.string().email(),
  phone: z.string().optional(),
  website: z.string().url().optional().or(z.literal("").transform(() => undefined)),
  address: z.string().optional(),
  status: z.custom<NGOStatus>().default('draft'),
});

export default function NGOEdit() {
  const { id } = useParams();
  const navigate = useNavigate();

  const form = useForm<z.infer<typeof schema>>({ resolver: zodResolver(schema), defaultValues: { status: 'draft', country: 'FI' } });

  useEffect(() => {
    if (id) {
      const n = getNGO(id);
      if (n) form.reset({ ...n });
    }
  }, [id]);

  const onSubmit = (values: z.infer<typeof schema>) => {
    const ngo = upsertNGO(values as any);
    toast({ title: "Saved", description: `${ngo.displayName} saved` });
    navigate(`/ngos/${ngo.id}`);
  };

  const onApprove = () => {
    const ngoId = id || form.getValues("id");
    if (!ngoId) return;
    setNGOStatus(ngoId, 'approved');
    toast({ title: "Approved", description: `NGO approved` });
  };

  return (
    <div className="space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">{id ? 'Edit NGO' : 'Create NGO'}</h1>
        <p className="text-sm text-muted-foreground">Core info and status</p>
      </header>

      <Card>
        <CardHeader>
          <CardTitle>NGO Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField control={form.control} name="legalName" render={({ field }) => (
                <FormItem>
                  <FormLabel>Legal name</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="displayName" render={({ field }) => (
                <FormItem>
                  <FormLabel>Display name</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="country" render={({ field }) => (
                <FormItem>
                  <FormLabel>Country (ISO-2)</FormLabel>
                  <FormControl><Input maxLength={2} {...field} /></FormControl>
                  <FormDescription>e.g., FI, TR, EN</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="email" render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl><Input type="email" {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="phone" render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="website" render={({ field }) => (
                <FormItem>
                  <FormLabel>Website</FormLabel>
                  <FormControl><Input placeholder="https://" {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="address" render={({ field }) => (
                <FormItem className="sm:col-span-2">
                  <FormLabel>Address</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="status" render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {(['draft','submitted','approved','rejected','suspended'] as NGOStatus[]).map(s => (
                        <SelectItem key={s} value={s}>{s}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )} />

              <div className="sm:col-span-2 flex gap-2">
                <Button type="submit" variant="hero">Save</Button>
                <Button type="button" onClick={onApprove}>Approve</Button>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="text-sm text-muted-foreground">
          Images & documents can be added later in MVP.
        </CardFooter>
      </Card>
    </div>
  );
}
