import { useMemo, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { listNGOs, NGO, NGOStatus, setNGOStatus } from "@/store/store";
import { toast } from "@/hooks/use-toast";

const STATUSES: NGOStatus[] = ['draft','submitted','approved','rejected','suspended'];

export default function NGOsList() {
  const [q, setQ] = useState("");
  const [status, setStatus] = useState<string>("");

  const data = useMemo(() => {
    return listNGOs().filter(n =>
      (!q || n.displayName.toLowerCase().includes(q.toLowerCase())) &&
      (!status || n.status === status)
    );
  }, [q, status]);

  const onAction = (n: NGO, s: NGOStatus) => {
    setNGOStatus(n.id, s);
    toast({ title: `NGO ${s}`, description: `${n.displayName} → ${s}` });
    // trigger rerender by bumping state
    setQ(q => q + "");
  };

  return (
    <div className="space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">NGOs</h1>
        <p className="text-sm text-muted-foreground">Create, review, and approve NGOs.</p>
      </header>

      <Card>
        <CardHeader className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <CardTitle>NGO List</CardTitle>
          <div className="flex gap-2">
            <Input placeholder="Search by name" value={q} onChange={e => setQ(e.target.value)} className="w-48" />
            <Select value={status} onValueChange={(v) => setStatus(v === "__all" ? "" : v)}>
              <SelectTrigger className="w-40"><SelectValue placeholder="All statuses" /></SelectTrigger>
              <SelectContent className="z-50 bg-popover">
                <SelectItem value="__all">All</SelectItem>
                {STATUSES.map(s => (<SelectItem key={s} value={s}>{s}</SelectItem>))}
              </SelectContent>
            </Select>
            <Button asChild variant="hero"><Link to="/ngos/new">+ New NGO</Link></Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Country</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.length === 0 && (
                <TableRow><TableCell colSpan={4} className="text-muted-foreground">No NGOs</TableCell></TableRow>
              )}
              {data.map(n => (
                <TableRow key={n.id}>
                  <TableCell className="font-medium"><Link to={`/ngos/${n.id}`}>{n.displayName}</Link></TableCell>
                  <TableCell>{n.country}</TableCell>
                  <TableCell className="capitalize">{n.status}</TableCell>
                  <TableCell className="space-x-2">
                    <Button size="sm" variant="secondary" asChild><Link to={`/ngos/${n.id}`}>Edit</Link></Button>
                    <Button size="sm" onClick={() => onAction(n, 'approved')}>Approve</Button>
                    <Button size="sm" variant="outline" onClick={() => onAction(n, 'rejected')}>Reject</Button>
                    <Button size="sm" variant="ghost" onClick={() => onAction(n, 'suspended')}>Suspend</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
