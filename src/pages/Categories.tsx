import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { listCategories, reorderCategories, toggleCategoryActive, upsertCategory } from "@/store/store";
import { toast } from "@/hooks/use-toast";

export default function Categories() {
  const [name, setName] = useState("");
  const [tick, setTick] = useState(0);

  const cats = useMemo(() => listCategories(), [tick]);

  const create = () => {
    if (!name.trim()) return;
    const c = upsertCategory({ name });
    toast({ title: "Category created", description: c.name });
    setName("");
    setTick(t => t + 1);
  };

  const move = (id: string, dir: -1 | 1) => {
    const ids = cats.map(c => c.id);
    const idx = ids.indexOf(id);
    const j = idx + dir;
    if (j < 0 || j >= ids.length) return;
    const swapped = [...ids];
    [swapped[idx], swapped[j]] = [swapped[j], swapped[idx]];
    reorderCategories(swapped);
    setTick(t => t + 1);
  };

  const toggle = (id: string, next: boolean) => {
    toggleCategoryActive(id, next);
    setTick(t => t + 1);
  };

  return (
    <div className="space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">Categories</h1>
        <p className="text-sm text-muted-foreground">Create, edit, reorder, and hide categories.</p>
      </header>

      <Card>
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <CardTitle>New Category</CardTitle>
          <div className="flex gap-2">
            <Input placeholder="Name (EN)" value={name} onChange={e => setName(e.target.value)} className="w-64" />
            <Button variant="hero" onClick={create}>Add</Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-14">Order</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Slug</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cats.length === 0 && (
                <TableRow><TableCell colSpan={5} className="text-muted-foreground">No categories</TableCell></TableRow>
              )}
              {cats.map((c, idx) => (
                <TableRow key={c.id}>
                  <TableCell className="tabular-nums">{idx+1}</TableCell>
                  <TableCell className="font-medium">{c.name}</TableCell>
                  <TableCell>{c.slug}</TableCell>
                  <TableCell>{c.isActive ? 'active' : 'hidden'}</TableCell>
                  <TableCell className="space-x-2">
                    <Button size="sm" variant="secondary" onClick={() => move(c.id, -1)}>Up</Button>
                    <Button size="sm" variant="secondary" onClick={() => move(c.id, 1)}>Down</Button>
                    <Button size="sm" onClick={() => toggle(c.id, !c.isActive)}>{c.isActive ? 'Hide' : 'Unhide'}</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
