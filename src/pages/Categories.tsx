import { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { listCategories, reorderCategories, toggleCategoryActive, upsertCategory, subscribeCategories, fetchCategories } from "@/store/store";
import { toast } from "@/hooks/use-toast";

export default function Categories() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [color, setColor] = useState("");
  const [orderInput, setOrderInput] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(true);
  const [parentCategory, setParentCategory] = useState<string>("");
  const [slug, setSlug] = useState("");
  const [subSelected, setSubSelected] = useState<Set<string>>(new Set());
  const [cats, setCats] = useState(() => listCategories());

  useEffect(() => {
    // Initial fetch with error handling
    fetchCategories()
      .then(setCats)
      .catch((e) => {
        toast({ title: "Failed to load categories", description: e?.message || String(e), variant: "destructive" });
      });
    // Live subscription, with fallback error callback
    const unsub = subscribeCategories(setCats, (e) => {
      toast({ title: "Live updates unavailable", description: e?.message || String(e), variant: "destructive" });
    });
    return () => unsub();
  }, []);

  const create = async (): Promise<void> => {
    if (!name.trim()) {
      toast({ title: "Name is required", variant: "destructive" });
      return;
    }
    let order: number | undefined = undefined;
    if (orderInput.trim()) {
      const n = Number(orderInput);
      if (!Number.isFinite(n) || n < 0) {
        toast({ title: "Invalid order value", description: "Order must be a non-negative number", variant: "destructive" });
        return;
      }
      order = n;
    }
    try {
      const c = await upsertCategory({
        name: name.trim(),
        description: description.trim() || undefined,
        color: color.trim() || null,
        order,
        isActive,
        parentCategory: parentCategory || null,
        slug: slug.trim() || undefined,
        subCategories: Array.from(subSelected),
      });
      toast({ title: "Category created", description: c.name });
      setName("");
      setDescription("");
      setColor("");
      setOrderInput("");
      setIsActive(true);
      setParentCategory("");
      setSlug("");
      setSubSelected(new Set());
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      toast({ title: "Failed to create category", description: msg, variant: "destructive" });
    }
  };

  const move = async (id: string, dir: -1 | 1): Promise<void> => {
    const ids = cats.map(c => c.id);
    const idx = ids.indexOf(id);
    const j = idx + dir;
    if (j < 0 || j >= ids.length) return;
    const swapped = [...ids];
    [swapped[idx], swapped[j]] = [swapped[j], swapped[idx]];
    try {
      await reorderCategories(swapped);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      toast({ title: "Failed to reorder", description: msg, variant: "destructive" });
    }
  };

  const toggle = async (id: string, next: boolean): Promise<void> => {
    try {
      await toggleCategoryActive(id, next);
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      toast({ title: "Failed to update status", description: msg, variant: "destructive" });
    }
  };

  return (
    <div className="space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">Categories</h1>
        <p className="text-sm text-muted-foreground">Create, edit, reorder, and hide categories.</p>
      </header>

      <Card>
        <CardHeader>
          <CardTitle>New Category</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label>Name</Label>
              <Input placeholder="Education" value={name} onChange={e => setName(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Slug (optional)</Label>
              <Input placeholder="education" value={slug} onChange={e => setSlug(e.target.value)} />
            </div>
            <div className="space-y-2 sm:col-span-2">
              <Label>Description</Label>
              <Textarea placeholder="Education description" value={description} onChange={e => setDescription(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Color</Label>
              <Input placeholder="blue" value={color} onChange={e => setColor(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Order</Label>
              <Input type="number" placeholder="0" value={orderInput} onChange={e => setOrderInput(e.target.value)} />
            </div>
            <div className="flex items-center gap-2">
              <Switch id="isActive" checked={isActive} onCheckedChange={setIsActive} />
              <Label htmlFor="isActive">Active</Label>
            </div>
            <div className="space-y-2">
              <Label>Parent Category</Label>
              <Select
                value={parentCategory === "" ? "__none" : parentCategory}
                onValueChange={(v) => setParentCategory(v === "__none" ? "" : v)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="None" />
                </SelectTrigger>
                <SelectContent className="bg-popover">
                  <SelectItem value="__none">None</SelectItem>
                  {cats.map(c => (
                    <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 sm:col-span-2">
              <Label>Sub-categories</Label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-48 overflow-auto border rounded-md p-2">
                {cats.map(c => (
                  <label key={c.id} className="flex items-center gap-2 text-sm">
                    <Checkbox
                      checked={subSelected.has(c.id)}
                      onCheckedChange={(checked) => {
                        setSubSelected(prev => {
                          const next = new Set(prev);
                          if (checked) next.add(c.id); else next.delete(c.id);
                          return next;
                        });
                      }}
                    />
                    <span>{c.name}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button variant="hero" onClick={create}>Add</Button>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-14">Order</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Slug</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cats.length === 0 && (
                <TableRow><TableCell colSpan={6} className="text-muted-foreground">No categories</TableCell></TableRow>
              )}
              {cats.map((c, idx) => (
                <TableRow key={c.id}>
                  <TableCell className="tabular-nums">{idx+1}</TableCell>
                  <TableCell className="font-medium">{c.name}</TableCell>
                  <TableCell className="max-w-[24ch] truncate" title={c.description}>{c.description}</TableCell>
                  <TableCell>{c.slug}</TableCell>
                  <TableCell>{c.isActive ? 'active' : 'hidden'}</TableCell>
                  <TableCell className="space-x-2">
                    <Button size="sm" variant="secondary" onClick={() => move(c.id, -1)}>Up</Button>
                    <Button size="sm" variant="secondary" onClick={() => move(c.id, 1)}>Down</Button>
                    <Button size="sm" onClick={() => toggle(c.id, !c.isActive)}>{c.isActive ? 'Hide' : 'Unhide'}</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
