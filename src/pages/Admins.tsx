import { useEffect, useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { addAdminEmail, deleteAdmin, fetchAdmins, subscribeAdmins, AdminUser } from '@/store/store';
import { toast } from '@/hooks/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';

function isValidEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

export default function Admins() {
  const [admins, setAdmins] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [newEmail, setNewEmail] = useState<string>('');

  useEffect(() => {
    fetchAdmins().then(setAdmins).finally(() => setLoading(false));
    console.log('fetchAdmins', admins);
    const unsub = subscribeAdmins(setAdmins);
    return () => unsub();
  }, []);

  const addAdmin = async (): Promise<void> => {
    const email = newEmail.trim();
    if (!isValidEmail(email)) {
      toast({ title: 'Invalid email address', variant: 'destructive' });
      return;
    }
    try {
      await addAdminEmail(email);
      toast({ title: 'Admin added', description: email });
      setNewEmail('');
    } catch (e) {
      const msg = e instanceof Error ? e.message : String(e);
      toast({ title: 'Failed to add admin', description: msg, variant: 'destructive' });
    }
  };

  const confirmDelete = (id: string, email: string) => (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button size="sm" variant="destructive">Remove</Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove admin?</AlertDialogTitle>
          <AlertDialogDescription>
            This will revoke admin access for {email}. This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={async () => {
              try {
                await deleteAdmin(id);
                toast({ title: 'Admin removed', description: email });
              } catch (e) {
                const msg = e instanceof Error ? e.message : String(e);
                toast({ title: 'Failed to remove admin', description: msg, variant: 'destructive' });
              }
            }}
          >
            Remove
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  return (
    <div className="space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">Admin Management</h1>
        <p className="text-sm text-muted-foreground">Manage who can access the admin dashboard.</p>
      </header>

      <Card>
        <CardHeader>
          <CardTitle>Add Administrator</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-2">
          <Input
            placeholder="Email address"
            type="email"
            value={newEmail}
            onChange={(e) => setNewEmail(e.target.value)}
            className="w-80"
          />
          <Button variant="hero" onClick={addAdmin} disabled={!newEmail || !isValidEmail(newEmail)}>Add</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Current Administrators</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Added</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading && (
                <TableRow><TableCell colSpan={3} className="text-muted-foreground">Loading…</TableCell></TableRow>
              )}
              {!loading && admins.length === 0 && (
                <TableRow><TableCell colSpan={3} className="text-muted-foreground">No admins</TableCell></TableRow>
              )}
              {admins.map(a => (
                <TableRow key={a.id}>
                  <TableCell className="font-medium">{a.email}</TableCell>
                  <TableCell>{new Date(a.createdAt).toLocaleString()}</TableCell>
                  <TableCell>{confirmDelete(a.id, a.email)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}


