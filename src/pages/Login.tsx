import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/lib/auth";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function Login() {
  const navigate = useNavigate();
  const { user, isAdmin, loading, error, signInWithGoogle } = useAuth();

  useEffect(() => {
    if (!loading && user && isAdmin) navigate("/");
  }, [user, isAdmin, loading, navigate]);

  return (
    <div className="max-w-md mx-auto space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">Admin Sign in</h1>
        <p className="text-sm text-muted-foreground">Sign in with Google to access the admin dashboard.</p>
      </header>
      <Card>
        <CardHeader>
          <CardTitle>Continue with Google</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {error && <div className="text-sm text-destructive">{error}</div>}
          <Button variant="hero" onClick={() => signInWithGoogle().catch(() => {})} disabled={loading}>
            Continue with Google
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
