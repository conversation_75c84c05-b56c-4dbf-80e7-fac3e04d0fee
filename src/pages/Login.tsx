import { Button } from "@/components/ui/button";

export default function Login() {
  return (
    <div className="max-w-md mx-auto space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">Sign in</h1>
        <p className="text-sm text-muted-foreground">Email + password login will be enabled once Supabase is connected.</p>
      </header>
      <div className="rounded-lg border p-4">
        <p className="text-sm text-muted-foreground">
          To enable authentication and data persistence, connect your Lovable project to Supabase using the green button in the top right.
        </p>
        <div className="mt-3">
          <Button variant="hero" disabled>Continue</Button>
        </div>
      </div>
    </div>
  );
}
