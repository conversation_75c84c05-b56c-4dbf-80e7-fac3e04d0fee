import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/lib/auth";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

export default function Login() {
  const navigate = useNavigate();
  const { user, isAdmin, loading, error, signInWithGoogle, signInWithEmail, registerWithEmail, resetPassword } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [regEmail, setRegEmail] = useState("");
  const [regPassword, setRegPassword] = useState("");
  const [resetEmail, setResetEmail] = useState("");

  useEffect(() => {
    if (!loading && user && isAdmin) navigate("/");
  }, [user, isAdmin, loading, navigate]);

  return (
    <div className="max-w-md mx-auto space-y-6">
      <header>
        <h1 className="text-2xl font-semibold">Admin Access</h1>
        <p className="text-sm text-muted-foreground">Sign in to continue</p>
      </header>
      <Tabs defaultValue="signin" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="signin">Sign in</TabsTrigger>
          <TabsTrigger value="register">Create account</TabsTrigger>
          <TabsTrigger value="reset">Reset password</TabsTrigger>
        </TabsList>

        <TabsContent value="signin">
          <Card>
            <CardHeader>
              <CardTitle>Sign in</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {error && <div className="text-sm text-destructive">{error}</div>}
              <Input type="email" placeholder="Email" value={email} onChange={e => setEmail(e.target.value)} />
              <Input type="password" placeholder="Password" value={password} onChange={e => setPassword(e.target.value)} />
              <div className="flex gap-2">
                <Button className="w-full" onClick={() => signInWithEmail(email, password).catch(() => {})} disabled={loading || !email || !password}>Sign in</Button>
                <Button variant="secondary" className="w-full" onClick={() => signInWithGoogle().catch(() => {})} disabled={loading}>Google</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="register">
          <Card>
            <CardHeader>
              <CardTitle>Create account</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {error && <div className="text-sm text-destructive">{error}</div>}
              <Input type="email" placeholder="Email" value={regEmail} onChange={e => setRegEmail(e.target.value)} />
              <Input type="password" placeholder="Password (min 6 chars)" value={regPassword} onChange={e => setRegPassword(e.target.value)} />
              <Button className="w-full" onClick={() => registerWithEmail(regEmail, regPassword).catch(() => {})} disabled={loading || !regEmail || regPassword.length < 6}>Create account</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reset">
          <Card>
            <CardHeader>
              <CardTitle>Reset password</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {error && <div className="text-sm text-destructive">{error}</div>}
              <Input type="email" placeholder="Email" value={resetEmail} onChange={e => setResetEmail(e.target.value)} />
              <Button className="w-full" onClick={() => resetPassword(resetEmail).catch(() => {})} disabled={loading || !resetEmail}>Send reset email</Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
