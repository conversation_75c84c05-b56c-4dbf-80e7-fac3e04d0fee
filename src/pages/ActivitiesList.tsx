import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Activity, ActivityStatus, listActivities, listCategories, listNGOs, upsertActivity, setActivityStatus } from "@/store/store";
import { toast } from "@/hooks/use-toast";

const STATUSES: ActivityStatus[] = ['draft','submitted','approved','live','completed'];

export default function ActivitiesList() {
  const ngos = useMemo(() => listNGOs(), []);
  const cats = useMemo(() => listCategories(), []);
  const [status, setStatus] = useState<string>("");
  const [ngoFilter, setNgoFilter] = useState<string>("");
  const [catFilter, setCatFilter] = useState<string>("");

  const data = useMemo(() => {
    return listActivities().filter(a =>
      (!status || a.status === status) &&
      (!ngoFilter || a.ngoId === ngoFilter) &&
      (!catFilter || a.categoryId === catFilter)
    );
  }, [status, ngoFilter, catFilter]);

  const [newNgo, setNewNgo] = useState<string>("");
  const [newTitle, setNewTitle] = useState("");
  const [newCat, setNewCat] = useState<string>("");

  const create = () => {
    if (!newNgo || !newTitle) return;
    const a = upsertActivity({ ngoId: newNgo, title: newTitle, categoryId: newCat, status: 'draft' });
    toast({ title: "Activity created", description: a.title });
    setNewTitle("");
  };

  const onAction = (a: Activity, s: ActivityStatus) => {
    setActivityStatus(a.id, s);
    toast({ title: `Activity ${s}`, description: a.title });
    setStatus(st => st + "");
  };

  return (
    <div className="space-y-4">
      <header>
        <h1 className="text-2xl font-semibold">Activities</h1>
        <p className="text-sm text-muted-foreground">Create and approve activities.</p>
      </header>

      <Card>
        <CardHeader className="flex flex-col gap-3">
          <CardTitle>New Activity</CardTitle>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select value={newNgo} onValueChange={setNewNgo}>
              <SelectTrigger className="w-52"><SelectValue placeholder="Select NGO" /></SelectTrigger>
              <SelectContent className="z-50 bg-popover">
                {ngos.map(n => (<SelectItem key={n.id} value={n.id}>{n.displayName}</SelectItem>))}
              </SelectContent>
            </Select>
            <Input placeholder="Title" value={newTitle} onChange={e => setNewTitle(e.target.value)} className="w-64" />
            <Select value={newCat} onValueChange={(v) => setNewCat(v === "__none" ? "" : v)}>
              <SelectTrigger className="w-52"><SelectValue placeholder="Category (optional)" /></SelectTrigger>
              <SelectContent className="z-50 bg-popover">
                <SelectItem value="__none">None</SelectItem>
                {cats.map(c => (<SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>))}
              </SelectContent>
            </Select>
            <Button variant="hero" onClick={create}>Create</Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Select value={status} onValueChange={(v) => setStatus(v === "__all" ? "" : v)}>
              <SelectTrigger className="w-40"><SelectValue placeholder="All statuses" /></SelectTrigger>
              <SelectContent className="z-50 bg-popover">
                <SelectItem value="__all">All</SelectItem>
                {STATUSES.map(s => (<SelectItem key={s} value={s}>{s}</SelectItem>))}
              </SelectContent>
            </Select>
            <Select value={ngoFilter} onValueChange={(v) => setNgoFilter(v === "__all" ? "" : v)}>
              <SelectTrigger className="w-48"><SelectValue placeholder="Filter by NGO" /></SelectTrigger>
              <SelectContent className="z-50 bg-popover">
                <SelectItem value="__all">All NGOs</SelectItem>
                {ngos.map(n => (<SelectItem key={n.id} value={n.id}>{n.displayName}</SelectItem>))}
              </SelectContent>
            </Select>
            <Select value={catFilter} onValueChange={(v) => setCatFilter(v === "__all" ? "" : v)}>
              <SelectTrigger className="w-48"><SelectValue placeholder="Filter by Category" /></SelectTrigger>
              <SelectContent className="z-50 bg-popover">
                <SelectItem value="__all">All Categories</SelectItem>
                {cats.map(c => (<SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>))}
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>NGO</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.length === 0 && (
                <TableRow><TableCell colSpan={4} className="text-muted-foreground">No activities</TableCell></TableRow>
              )}
              {data.map(a => (
                <TableRow key={a.id}>
                  <TableCell className="font-medium">{a.title}</TableCell>
                  <TableCell>{ngos.find(n => n.id === a.ngoId)?.displayName || a.ngoId}</TableCell>
                  <TableCell className="capitalize">{a.status}</TableCell>
                  <TableCell className="space-x-2">
                    <Button size="sm" onClick={() => onAction(a, 'approved')}>Approve</Button>
                    <Button size="sm" variant="outline" onClick={() => onAction(a, 'submitted')}>Submit</Button>
                    <Button size="sm" variant="ghost" onClick={() => onAction(a, 'completed')}>Complete</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
